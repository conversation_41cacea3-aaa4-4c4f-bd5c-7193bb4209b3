"use client";

import React, { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  PaymentElement,
  ExpressCheckoutElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface SimplePaymentFormProps {
  paymentIntentId?: string;
  amount: number;
  currency: string;
  productName: string;
  isEscrow?: boolean;
  orderId?: string;
  transactionId?: string;
  userId?: string;
  sellerId?: string;
  onSuccess?: (paymentIntent: any) => void;
  onError?: (error: string) => void;
}

function CheckoutForm({
  orderId,
  transactionId,
  userId,
  sellerId,
  amount,
  currency,
  isEscrow,
  clientSecret,
  onSuccess,
  onError
}: {
  orderId?: string;
  transactionId?: string;
  userId?: string;
  sellerId?: string;
  amount: number;
  currency: string;
  isEscrow: boolean;
  clientSecret?: string;
  onSuccess?: (paymentIntent: any) => void;
  onError?: (error: string) => void;
}) {
  const stripe = useStripe();
  const elements = useElements();
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<string | null>(null);
  const [expressCheckoutMessage, setExpressCheckoutMessage] = useState<string | null>(null);
  const [expressCheckoutReady, setExpressCheckoutReady] = useState(false);



  // Debug: Check Stripe instance when it becomes available
  useEffect(() => {
    if (stripe) {
      console.log('🔍 Stripe instance available in CheckoutForm:', stripe);
    } else {
      console.log('❌ Stripe instance not available in CheckoutForm');
    }
  }, [stripe]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe || !elements) {
      return;
    }

    setIsLoading(true);
    setMessage(null);

    // Use redirect: "if_required" to handle success in our callback
    const returnUrl = new URL(`${window.location.origin}/payment-success`);
    if (orderId) returnUrl.searchParams.set('order_id', orderId);
    if (transactionId) returnUrl.searchParams.set('transaction_id', transactionId);
    if (userId) returnUrl.searchParams.set('user_id', userId);
    if (sellerId) returnUrl.searchParams.set('seller_id', sellerId);
    returnUrl.searchParams.set('amount', amount.toString());
    returnUrl.searchParams.set('currency', currency);
    returnUrl.searchParams.set('is_escrow', isEscrow.toString());

    // Submit the form first (required by Stripe)
    const submitResult = await elements.submit();
    if (submitResult.error) {
      setMessage(submitResult.error.message || "Form validation failed");
      onError?.(submitResult.error.message || "Form validation failed");
      setIsLoading(false);
      return;
    }

    // Check if we already have a client secret from existing payment intent
    try {
      let clientSecretToUse = clientSecret; // Use existing client secret if available

      // Only create new payment intent if we don't have an existing client secret
      if (!clientSecretToUse) {
        console.log('🔄 Creating NEW payment intent with data:', {
          amount,
          currency,
          userId,
          sellerId,
          orderId,
          isEscrow,
        });

        // Use the correct endpoint based on escrow setting
        const endpoint = isEscrow ? '/api/escrow/create-payment-intent' : '/api/create-payment-intent';
        console.log('🎯 Using endpoint:', endpoint);

        const response = await fetch(endpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            amount,
            currency,
            userId,
            sellerId,
            orderId,
            isEscrow,
          }),
        });

        console.log('📡 Payment intent response status:', response.status);

        const data = await response.json();
        console.log('📄 Payment intent response data:', data);

        if (!data.success) {
          throw new Error(data.error || 'Failed to create payment intent');
        }

        clientSecretToUse = data.clientSecret;
      } else {
        console.log('✅ Using existing client secret from payment intent');
      }

      if (!clientSecretToUse) {
        throw new Error('No client secret available for payment confirmation');
      }

      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        clientSecret: clientSecretToUse,
        confirmParams: {
          return_url: returnUrl.toString(),
        },
        redirect: "if_required"
      });

      if (error) {
        console.error('❌ Payment confirmation error:', error);
        console.error('   Error type:', error.type);
        console.error('   Error code:', error.code);
        console.error('   Error message:', error.message);

        if (error.type === "card_error" || error.type === "validation_error") {
          setMessage(error.message || "An error occurred");
          onError?.(error.message || "An error occurred");
        } else {
          const errorMsg = `Payment failed: ${error.message || "An unexpected error occurred"}`;
          setMessage(errorMsg);
          onError?.(errorMsg);
        }
      } else if (paymentIntent) {
        // Payment succeeded without redirect - handle success
        if (paymentIntent.status === "succeeded") {
          setMessage("Payment succeeded!");
          onSuccess?.(paymentIntent);
        } else if (paymentIntent.status === "requires_capture") {
          setMessage("Payment authorized! (Escrow)");
          onSuccess?.(paymentIntent);
        } else if (paymentIntent.status === "processing") {
          setMessage("Payment is processing...");
          // You might want to poll for status or handle this case
        } else {
          setMessage(`Payment status: ${paymentIntent.status}`);
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setMessage(errorMessage);
      onError?.(errorMessage);
    }

    setIsLoading(false);
  };



  // Enhanced logging for Apple Pay debugging
  console.log('🍎 APPLE PAY DEBUGGING');
  console.log('🔍 Payment details:', { amount, currency, isEscrow });
  console.log('🔍 Current domain:', window.location.hostname);
  console.log('🔍 Protocol:', window.location.protocol);
  console.log('🔍 User Agent:', navigator.userAgent);
  console.log('🔍 Apple Pay Session available:', typeof (window as any).ApplePaySession !== 'undefined');

  if (typeof (window as any).ApplePaySession !== 'undefined') {
    console.log('🔍 Apple Pay can make payments:', (window as any).ApplePaySession.canMakePayments());
    console.log('🔍 Apple Pay version:', (window as any).ApplePaySession.supportsVersion ? 'Supported' : 'Not supported');
  }

  const handleExpressCheckout = async (event: any) => {
    console.log('🚀 Express checkout initiated:', event);
    // For mode-based Elements, we need to create payment intent during confirmation
    try {
      // Use the correct endpoint based on escrow setting
      const endpoint = isEscrow ? '/api/escrow/create-payment-intent' : '/api/create-payment-intent';
      console.log('🎯 Express checkout using endpoint:', endpoint);

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          amount,
          currency,
          userId,
          sellerId,
          orderId,
          isEscrow,
        }),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Failed to create payment intent');
      }

      // Submit the form first (required by Stripe)
      const submitResult = await elements!.submit();
      if (submitResult.error) {
        setExpressCheckoutMessage(submitResult.error.message || "Form validation failed");
        onError?.(submitResult.error.message || "Form validation failed");
        return;
      }

      const { error, paymentIntent } = await stripe!.confirmPayment({
        elements: elements!,
        clientSecret: data.clientSecret,
        confirmParams: {
          return_url: `${window.location.origin}/payment-success`,
        },
        redirect: "if_required"
      });

      if (error) {
        setExpressCheckoutMessage(error.message || "Express checkout failed");
        onError?.(error.message || "Express checkout failed");
      } else if (paymentIntent) {
        if (paymentIntent.status === "succeeded") {
          setExpressCheckoutMessage("Payment succeeded!");
          onSuccess?.(paymentIntent);
        } else if (paymentIntent.status === "requires_capture") {
          setExpressCheckoutMessage("Payment authorized! (Escrow)");
          onSuccess?.(paymentIntent);
        }
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setExpressCheckoutMessage(errorMessage);
      onError?.(errorMessage);
    }
  };

  return (
    <form id="payment-form" onSubmit={handleSubmit}>
      {/* Express Checkout Element (Apple Pay/Google Pay/Link) */}
      <div className="mb-4">
        <ExpressCheckoutElement
          onConfirm={handleExpressCheckout}
          onReady={({ availablePaymentMethods }) => {
            console.log('🚀 Express Checkout ready!');
            console.log('💳 Available payment methods:', availablePaymentMethods);
            console.log('🔍 Current URL:', window.location.href);
            console.log('🔍 Domain:', window.location.hostname);
            console.log('🔍 Protocol:', window.location.protocol);

            if (availablePaymentMethods && Object.keys(availablePaymentMethods).length > 0) {
              setExpressCheckoutReady(true);
              console.log('✅ Payment methods available:', Object.keys(availablePaymentMethods));

              // Log each available payment method with details
              Object.entries(availablePaymentMethods).forEach(([method, details]) => {
                console.log(`🔹 ${method}:`, details);

                // Special logging for Apple Pay
                if (method === 'applePay') {
                  console.log('🍎 Apple Pay Details:');
                  console.log('   - Available:', true);
                  console.log('   - Browser:', navigator.userAgent.includes('Safari') ? 'Safari' : 'Other');
                  console.log('   - ApplePaySession:', typeof (window as any).ApplePaySession !== 'undefined');
                  if (typeof (window as any).ApplePaySession !== 'undefined') {
                    console.log('   - Can Make Payments:', (window as any).ApplePaySession.canMakePayments());
                  }
                }
              });
            } else {
              console.log('❌ No payment methods available');
              console.log('🔧 Detailed Troubleshooting:');
              console.log('1. Domain Registration:');
              console.log('   - Current domain:', window.location.hostname);
              console.log('   - Check if registered at: https://dashboard.stripe.com/settings/payment_method_domains');
              console.log('2. HTTPS Requirement:');
              console.log('   - Current protocol:', window.location.protocol);
              console.log('   - Apple Pay requires HTTPS in production');
              console.log('3. Browser Support:');
              console.log('   - User Agent:', navigator.userAgent);
              console.log('   - Apple Pay works best in Safari');
              console.log('4. Device Setup:');
              console.log('   - ApplePaySession available:', typeof (window as any).ApplePaySession !== 'undefined');
              if (typeof (window as any).ApplePaySession !== 'undefined') {
                console.log('   - Can make payments:', (window as any).ApplePaySession.canMakePayments());
              }
              setExpressCheckoutReady(false);
            }
          }}
          onLoadError={(event) => {
            console.log('❌ Express Checkout load error:', event);
          }}
          options={{
            // Configure payment methods based on Stripe documentation
            paymentMethods: {
              applePay: 'auto', // Show Apple Pay when available
              googlePay: 'auto', // Show Google Pay when available
              link: 'auto', // Show Link when available
              paypal: 'auto', // Show PayPal when available
              // amazonPay: 'auto', // Show Amazon Pay when available
            },
            // Set button theme
            buttonTheme: {
              applePay: 'black',
              googlePay: 'black',
            },
            // Set layout
            layout: {
              maxColumns: 3,
              maxRows: 1,
              overflow: 'auto',
            },
          }}
        />
        {/* Show divider and status when Express Checkout is ready */}
        {expressCheckoutReady ? (
          <div>
            <div className="flex items-center my-4">
              <div className="flex-1 border-t border-gray-300"></div>
              <span className="px-3 text-gray-500 text-sm">or pay with card</span>
              <div className="flex-1 border-t border-gray-300"></div>
            </div>

            {/* Success status */}
            <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded text-sm">
              <p className="font-medium text-green-800 mb-2">✅ Express Checkout Active</p>
              <div className="text-green-700 space-y-1">
                <p>� Currency: {currency.toUpperCase()}</p>
                <p>🌍 Environment: Production (HTTPS)</p>
                <p className="text-xs mt-2">
                  One-click payment methods are available above. Check console for details.
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div>
            {/* No payment methods available */}
            <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded text-sm">
              <p className="font-medium text-yellow-800 mb-2">⚠️ No Express Payment Methods Available</p>
              <div className="text-yellow-700 space-y-1">
                <p>💳 Currency: {currency.toUpperCase()}</p>
                <p>🌍 Environment: Production (HTTPS)</p>
                <p>🔍 Google Pay API: {typeof window !== 'undefined' && (window as any).google?.payments?.api ? '✅ Available' : '❌ Not Available'}</p>
                <p>🔍 Apple Pay: {typeof window !== 'undefined' && (window as any).ApplePaySession ? '✅ Supported' : '❌ Not Supported'}</p>
                <p className="text-xs mt-2">
                  <strong>To enable payment methods:</strong><br/>
                  • <strong>Google Pay:</strong> Sign in to Google, add cards at pay.google.com<br/>
                  • <strong>Apple Pay:</strong> Use Safari on Mac/iOS with Touch ID/Face ID<br/>
                  • <strong>Link:</strong> Should work automatically<br/>
                  • <strong>PayPal/Others:</strong> Must be activated in Stripe Dashboard
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      <PaymentElement id="payment-element" />
      <button 
        disabled={isLoading || !stripe || !elements} 
        id="submit"
        className="w-full mt-6 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-3 px-4 rounded transition-colors"
      >
        <span id="button-text">
          {isLoading ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Processing...
            </div>
          ) : (
            "Pay now"
          )}
        </span>
      </button>
      {message && (
        <div className={`mt-4 p-3 rounded ${
          message.includes("processing") 
            ? "bg-blue-100 text-blue-700 border border-blue-400"
            : "bg-red-100 text-red-700 border border-red-400"
        }`}>
          {message}
        </div>
      )}
    </form>
  );
}

export default function SimplePaymentForm({
  paymentIntentId,
  amount,
  currency,
  productName,
  isEscrow = false,
  orderId,
  transactionId,
  userId,
  sellerId,
  onSuccess,
  onError
}: SimplePaymentFormProps) {
  const [clientSecret, setClientSecret] = useState("");
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

//   // Load Google Pay API
//   useEffect(() => {
//     const loadGooglePayAPI = () => {
//       if (typeof window !== 'undefined' && !(window as any).google?.payments?.api) {
//         const script = document.createElement('script');
//         script.src = 'https://pay.google.com/gp/p/js/pay.js';
//         script.async = true;
//         script.onload = () => {
//           console.log('✅ Google Pay API loaded successfully');
//         };
//         script.onerror = () => {
//           console.log('❌ Failed to load Google Pay API');
//         };
//         document.head.appendChild(script);
//       }
//     };

//     loadGooglePayAPI();
//   }, []);
// console.log('Hi -->',currency);
  useEffect(() => {
    // Debug: Check if Google Pay is available
    
    if (typeof window !== 'undefined') {
      console.log('🔍 Window object available');
      console.log('🔍 Google object:', (window as any).google);

      if ((window as any).google?.payments?.api) {
        console.log('🔍 Google Pay API available');

        // Test Google Pay readiness
        const paymentsClient = new (window as any).google.payments.api.PaymentsClient({
          environment: 'TEST' // Change to 'PRODUCTION' for live
        });

        const isReadyToPayRequest = {
          apiVersion: 2,
          apiVersionMinor: 0,
          allowedPaymentMethods: [{
            type: 'CARD',
            parameters: {
              allowedAuthMethods: ['PAN_ONLY', 'CRYPTOGRAM_3DS'],
              allowedCardNetworks: ['MASTERCARD', 'VISA']
            }
          }]
        };

        paymentsClient.isReadyToPay(isReadyToPayRequest)
          .then((response: any) => {
            console.log('🔍 Google Pay readiness:', response);
          })
          .catch((error: any) => {
            console.log('❌ Google Pay readiness error:', error);
          });
      } else {
        console.log('❌ Google Pay API not available');
        console.log('🔍 Available on window:', Object.keys(window as any));
      }
    }

    const getPaymentIntent = async () => {
      try {
        if (paymentIntentId) {
          console.log('🔄 Retrieving existing payment intent...', paymentIntentId);
          
          const response = await fetch(`/api/payment-intent/${paymentIntentId}`, {
            method: "GET",
            headers: { "Content-Type": "application/json" },
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data = await response.json();
          console.log('✅ Payment intent retrieved:', data);
          
          if (data.clientSecret) {
            setClientSecret(data.clientSecret);
            console.log('✅ Client secret set');
          } else {
            throw new Error(data.error || "No client secret received");
          }
        } else {
          throw new Error("No payment intent ID provided");
        }
      } catch (err) {
        console.error('❌ Error retrieving payment intent:', err);
        const errorMessage = err instanceof Error ? err.message : "Unknown error";
        setError(errorMessage);
        onError?.(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    getPaymentIntent();
  }, [paymentIntentId, onError]);

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center p-8 space-y-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="text-gray-600">Loading payment form...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6 bg-red-50 border border-red-200 text-red-700 rounded-lg">
        <h3 className="font-semibold text-lg mb-2">Payment Error</h3>
        <p className="mb-4">{error}</p>
        <button 
          onClick={() => window.location.reload()} 
          className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  if (!clientSecret) {
    return (
      <div className="p-6 bg-yellow-50 border border-yellow-200 text-yellow-700 rounded-lg">
        <p>Initializing payment form...</p>
      </div>
    );
  }

  const appearance = {
    theme: 'stripe' as const,
    variables: {
      colorPrimary: '#2563eb',
      colorBackground: '#ffffff',
      colorText: '#1f2937',
      colorDanger: '#dc2626',
      fontFamily: 'system-ui, sans-serif',
      spacingUnit: '4px',
      borderRadius: '6px',
    },
  };

  // Elements configuration based on whether we have a client secret
  const elementsOptions = clientSecret ? {
    // Client secret mode (when paymentIntentId is provided)
    clientSecret: clientSecret,
    appearance,
    locale: 'en' as const,
  } : {
    // Mode-based configuration (when no paymentIntentId)
    mode: 'payment' as const,
    amount: amount,
    currency: currency.toLowerCase(), // Ensure currency is lowercase (e.g., "usd")
    // IMPORTANT: Set captureMethod to match the payment intent
    captureMethod: isEscrow ? 'manual' as const : 'automatic' as const,
    // Only include payment methods that work with PaymentElement
    // Apple Pay and Google Pay are handled by ExpressCheckoutElement
    paymentMethodTypes: ['card', 'link', 'paypal', 'crypto', 'klarna'],
    appearance,
    locale: 'en' as const,
  };

  console.log('⚙️ Elements configuration:', {
    hasClientSecret: !!clientSecret,
    hasPaymentIntentId: !!paymentIntentId,
    isEscrow,
    captureMethod: isEscrow ? 'manual' : 'automatic',
    mode: clientSecret ? 'client-secret' : 'payment-mode'
  });

  return (
    <div className="max-w-md mx-auto bg-white p-6 rounded-lg shadow-sm border">
      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-2 text-gray-900">{productName}</h2>
        <div className="text-gray-600">
          <p className="text-lg font-medium">
            {currency.toUpperCase()} {(amount / 100).toFixed(2)}
          </p>
          {isEscrow && (
            <p className="text-sm text-blue-600 mt-1">
              🔒 Escrow Payment - Funds held securely
            </p>
          )}
        </div>
      </div>
      
      <Elements options={elementsOptions} stripe={stripePromise}>
        <CheckoutForm
          orderId={orderId}
          transactionId={transactionId}
          userId={userId}
          sellerId={sellerId}
          amount={amount}
          currency={currency}
          isEscrow={isEscrow}
          clientSecret={clientSecret}
          onSuccess={onSuccess}
          onError={onError}
        />
      </Elements>
    </div>
  );
}
