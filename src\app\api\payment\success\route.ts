import { NextRequest, NextResponse } from 'next/server';
import { processPaymentSuccess, processPaymentFailure } from '@/services/postPaymentService';
import { Stripe } from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('🎉 Payment success webhook received:', body);

    const {
      paymentIntentId,
      orderId,
      transactionId,
      amount,
      currency,
      isEscrow,
      userId,
      sellerId,
      userEmail,
      userName,
      sellerName
    } = body;

    // Validate required fields
    if (!paymentIntentId || !orderId || !userId || !sellerId) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: paymentIntentId, orderId, userId, sellerId'
      }, { status: 400 });
    }

    // Verify payment intent with Stripe
    let paymentIntent;
    try {
      paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);
      console.log('✅ Payment intent verified:', {
        id: paymentIntent.id,
        status: paymentIntent.status,
        amount: paymentIntent.amount
      });
    } catch (stripeError) {
      console.error('❌ Failed to verify payment intent:', stripeError);
      return NextResponse.json({
        success: false,
        error: 'Invalid payment intent'
      }, { status: 400 });
    }

    // Check if payment actually succeeded or is authorized (for escrow)
    const validStatuses = ['succeeded', 'requires_capture'];
    if (!validStatuses.includes(paymentIntent.status)) {
      console.log('⚠️ Payment intent not in valid status:', paymentIntent.status);

      // Handle failed payment
      await processPaymentFailure({
        orderId,
        transactionId,
        error: `Payment status: ${paymentIntent.status}`,
        userId,
        sellerId,
        userName,
        sellerName
      });

      return NextResponse.json({
        success: false,
        error: `Payment not completed. Status: ${paymentIntent.status}`
      }, { status: 400 });
    }

    console.log('✅ Payment intent status is valid:', paymentIntent.status);

    // Get charge ID from payment intent
    const charges = paymentIntent.charges?.data;
    const chargeId = charges && charges.length > 0 ? charges[0].id : undefined;

    // Process successful payment
    console.log('🎯 Processing payment success with data:', {
      paymentIntentId,
      orderId,
      transactionId,
      amount: amount || paymentIntent.amount,
      currency: currency || paymentIntent.currency,
      isEscrow: isEscrow || false,
      userId,
      sellerId
    });

    const result = await processPaymentSuccess({
      paymentIntentId,
      orderId,
      transactionId,
      amount: amount || paymentIntent.amount,
      currency: currency || paymentIntent.currency,
      isEscrow: isEscrow || false,
      userId,
      sellerId,
      userEmail,
      userName,
      sellerName,
      chargeId
    });

    if (!result.success) {
      console.error('❌ Failed to process payment success:', result.error);
      return NextResponse.json({
        success: false,
        error: result.error
      }, { status: 500 });
    }

    console.log('✅ Payment success processed successfully');
    return NextResponse.json({
      success: true,
      message: 'Payment processed successfully',
      orderId,
      paymentIntentId,
      status: paymentIntent.status
    });

  } catch (error) {
    console.error('❌ Error in payment success endpoint:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
