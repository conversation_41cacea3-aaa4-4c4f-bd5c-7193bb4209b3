"use client";
import { getAllPostsTest } from "@/services/postService";
import { useCallback, useEffect, useRef, useState } from "react";
import { themes } from "../../../../theme";
import ImageCard from "./imageCard";

// ***************** for lens ***************//
import {
  LimitType,
  PublicationMetadataMainFocusType,
  PublicationType,
  useProfilesQuery,
  usePublicationsQuery,
} from "@/graphql/generated";
import { getLensProfileDetails, getLensProfilesById } from "@/services/lensService";
import React from "react";
import PostCardSkeleton from "@/components/CardSkeleton/PostCardSkeleton";
import { getUserById, getUserByPostId } from "@/services/usersServices";
import useAuth from "@/hook";
import useProfile from "@/hook/profileData";
import {
  AccountsBulkQuery,
  FollowingOrderBy,
  MainContentFocus,
  PageSize,
  PostType,
  useAccountsBulkQuery,
  useFollowingQuery,
  usePostsQuery,
} from "@/graphql/test/generated";
import { getId } from "@/services/authBridgeService";
import { useFilter } from "@/context/FilterContext";

export let exportData: any;

const PostCompTest = (props: any) => {
  const user = useAuth();
  const { filters, getServiceFilters } = useFilter();
  const { profileData: profileDataById } = useProfile(user?.userId || "");

  const [categoryData, setCategoryData] = useState<Record<string, any[]>>({});
  const [loading, setLoading] = useState<boolean>(true);
  const isFetched = useRef<boolean>(false);

  // Track individual loading states
  const [postsLoaded, setPostsLoaded] = useState(false);
  const [profilesLoaded, setProfilesLoaded] = useState(false);
  const [publicationsLoaded, setPublicationsLoaded] = useState(false);

  const fetchAllPosts = useCallback(async () => {
    try {
      setLoading(true);

      // Get complete filter object for service call
      const serviceFilters = getServiceFilters();
      const response = await getAllPostsTest(serviceFilters);

      if (response?.posts) {
        const categorizedData: Record<string, any[]> = {};
        const myFeed: any[] = [];
        const myFeedSet = new Set();

        // To remove duplicate posts globally
        const uniquePostsMap = new Map<string, any>();

        // Fetch user followers only once if logged in
        let userFollowings: string[] = [];
        if (user?.isLogin) {
          const userResponse = await getUserById(user?.userId);
          if (userResponse.success) {
            userFollowings = userResponse.user?.following || [];
          }
        }

        // Process posts asynchronously
        await Promise.all(
          response.posts.map(async (post: any) => {
            if (uniquePostsMap.has(post.id)) return; // Skip duplicate posts

            const [postCategory, userId] = await Promise.all([
              getUserCategory(post?.id),
              getUserIDForCheck(post?.id),
            ]);

            const normalizedCategory =
              post.category === "Storytelling" ? "Literature" : post.category;

            // Store unique posts
            uniquePostsMap.set(post.id, {
              ...post,
              category: normalizedCategory,
            });

            if (!categorizedData[normalizedCategory]) {
              categorizedData[normalizedCategory] = [];
            }
            categorizedData[normalizedCategory].push(post);
            // Add to "My Feed" if the user follows the post's author
            if (
              user?.isLogin &&
              userId &&
              userFollowings.includes(userId) &&
              !myFeedSet.has(post.id)
            ) {
              myFeedSet.add(post.id);
              myFeed.push({ ...post, category: normalizedCategory });
            }
          })
        );

        // Sort posts in each category by `created` field
        for (const category in categorizedData) {
          categorizedData[category].sort(
            (a, b) => new Date(a.created).getTime() - new Date(b.created).getTime()
          );
        }

        // Add "My Feed" if user is logged in
        if (user?.isLogin) {
          categorizedData["My Feed"] = myFeed;
        }

        // console.log({ categorizedData });

        setCategoryData(categorizedData);

        setPostsLoaded(true);
      }
    } catch (error) {
      console.error("Error fetching posts:", error);
    } finally {
      setLoading(false);
    }
  }, [profileDataById, filters]); // Keep dependency consistent with events function

  useEffect(() => {
    fetchAllPosts();
  }, [fetchAllPosts]); // Ensures function runs only when dependencies change

  // Function to get user category by event ID
  const getUserCategory = async (eventId: string) => {
    try {
      const response = await getUserByPostId(eventId);
      if (response.success) {
        const userData: any = response.users;
        return userData[0]?.categories[0]; // Return the category
      }
    } catch (error) {
      console.error("Error fetching user category:", error);
    }
    return null;
  };

  // Function to get user ID for checking against followers
  const getUserIDForCheck = async (eventId: string) => {
    try {
      const response = await getUserByPostId(eventId);
      if (response.success) {
        const userData: any = response.users;
        return userData[0]?.id; // Return the user ID
      }
    } catch (error) {
      console.error("Error fetching user ID:", error);
    }
    return null;
  };

  const chunkArray = (array: string[], chunkSize: number) => {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  };

  // ******************************************************************************* LENS DATA******************************************//

  // console.log(props.themeProperties.title);
  const category = props.themeProperties.title.toLowerCase();
  const id = "0x08cfd6";

  const [profiles, setProfiles] = useState<
    Array<{
      localName: string;
    }>
  >([]); // State for search input
  const [profilesmy, setProfilesMy] = useState<
    Array<{
      localName: string;
    }>
  >([]);
  const [profilesIds, setProfilesIds] = useState<string[]>(["0x89dc", "0x326c"]); // State for search input
  const [currentCursor, setCurrentCursor] = React.useState<string | null>(null);

  const {
    data: profileData,
    error: profileError,
    isLoading: loadingProfile,
  } = useAccountsBulkQuery(
    {
      request: {
        // usernames: profiles,
        usernames: props.themeProperties.title == "My Feed" ? profilesmy : profiles,
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled:
        props.themeProperties.title == "My Feed" ? profilesmy.length > 0 : profiles.length > 0, // Only execute when usernames array has at least one element
    }
  );
  // console.log({ profileData });

  const {
    isLoading: isLoadingPublications,
    data: publicationsData,
    error: publicationsError,
  } = usePostsQuery(
    {
      request: {
        filter: {
          authors: profilesIds, // joanakawaharalino
          postTypes: [PostType.Root],
          metadata: {
            mainContentFocus: [MainContentFocus.Image, MainContentFocus.Video],
          },
        },
        cursor: currentCursor,
        pageSize: PageSize.Fifty,
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled: profilesIds.length > 0, // Only execute query when profilesIds has at least one element
    }
  );

  // console.log({ publicationsData });

  useEffect(() => {
    // console.log("...........");

    const fetchLensProfilesByCategory = async (category: string) => {
      // get lens profiles
      const resp = await getLensProfilesById(category);
      const lens_profiles: Array<{
        localName: string;
      }> = resp?.lens_ids?.map((curr: any) => {
        return {
          localName: curr,
        };
      });

      // console.log({ lens_profiles });
      setProfiles(lens_profiles);
      // console.log({publicationsData});
    };

    fetchLensProfilesByCategory(category);
  }, []);

  useEffect(() => {
    if (profileData) {
      setProfilesLoaded(true);
      const lens_ids: string[] = profileData?.accountsBulk?.map(
        (curr: AccountsBulkQuery["accountsBulk"][0]) => curr.address
      );
      // console.log({ lens_ids });

      setProfilesIds(lens_ids); // Triggers the publications query
    }
  }, [profileData]);

  useEffect(() => {
    // if (profileData) console.log("Updated profileData:", profileData);
  }, [profileData]);

  useEffect(() => {
    if (publicationsData) {
      setPublicationsLoaded(true);
    }
  }, [publicationsData]);

  useEffect(() => {
    if (props.isLoading) {
      setLoading(true);
    }
  }, [props.isLoading]);

  // ✅ NEW: Set loading to false only after all data is loaded
  useEffect(() => {
    if (postsLoaded && profilesLoaded && publicationsLoaded && !props.isLoading) {
      setLoading(false);
    }
  }, [postsLoaded, profilesLoaded, publicationsLoaded]);

  const loadNextPage = () => {
    const nextCursor = publicationsData?.posts.pageInfo.next;
    // console.log({ nextCursor });

    if (nextCursor) {
      setCurrentCursor(nextCursor);
    }
  };

  exportData = publicationsData?.posts.items;
  // console.log(categoryData, "categoryData");

  // console.log(publicationsData?.posts.items[0].id, "publicationsData");

  // my feed
  const [currentFollowingCursor, setCurrentFollowingCursor] = useState<string | null>(null);
  const [userId, setUserId] = useState("");
  const containerRef = useRef<HTMLDivElement>(null);

  const getLensUserId = async (otherUserID: any) => {
    try {
      const resp = await getId({ id: user.userId });
      if (resp) {
        setUserId(resp?.lens_code);
      }
    } catch (error) {
      console.log({ error });
    }
  };

  useEffect(() => {
    getLensUserId(user.userId);
  }, [user.userId]);

  const { data: following, isLoading: following_loading } = useFollowingQuery(
    {
      request: {
        account: userId, // address
        pageSize: PageSize.Ten, // Changed to smaller page size for better pagination
        orderBy: FollowingOrderBy.Desc,
        cursor: currentFollowingCursor,
      },
    },
    {
      refetchOnWindowFocus: false,
      enabled: !!userId,
    }
  );

  interface FollowingItem {
    localName: any;
  }

  const [allFollowing, setAllFollowing] = useState<FollowingItem[]>([]);

  useEffect(() => {
    if (
      following?.following?.items &&
      following?.following?.items.length > 0 &&
      props.themeProperties.title == "My Feed"
    ) {
      const newArray: FollowingItem[] = following.following.items.map((item) => ({
        localName: item.following.username?.localName,
      }));

      // Add new items, avoiding duplicates
      if (props.themeProperties.title == "My Feed") {
        setAllFollowing((prev) => {
          const updatedFollowing = [...prev];

          newArray.forEach((newItem) => {
            // Check if this item already exists in the array
            if (
              !updatedFollowing.some((existingItem) => existingItem.localName === newItem.localName)
            ) {
              updatedFollowing.push(newItem);
            }
          });

          return updatedFollowing;
        });
      }

      // Add new profiles, avoiding duplicates
      setProfiles((prev) => {
        const updatedProfiles = [...prev];

        newArray.forEach((newItem) => {
          // Check if this item already exists in the array
          if (
            !updatedProfiles.some((existingItem) => existingItem.localName === newItem.localName)
          ) {
            updatedProfiles.push(newItem);
          }
        });

        return updatedProfiles;
      });
    }
  }, [following, props.themeProperties.title]);

  useEffect(() => {
    if (allFollowing.length > 0) {
      setProfilesMy(allFollowing);
    }
  }, [allFollowing]);
  return (
    <>
      {loading ? (
        <div className="">
          <PostCardSkeleton count={3} />
        </div>
      ) : (
        <>
          <div className="w-full mt-0">
            {Object.entries(themes).map(([themeName, themeProperties]) => (
              <div key={themeName}>
                {props.themeProperties.title === themeProperties.title && (
                  <div>
                    {(() => {
                      const currentCategory = themeProperties.title;

                      // Check if filters are applied and if current category is in selected categories
                      if (filters.categories && filters.categories.length > 0) {
                        if (!filters.categories.includes(currentCategory)) {
                          // Current category is not in selected filters, show no data
                          return (
                            <div className="w-full mt-2 p-4 bg-gray-50 rounded-md border border-gray-200 text-center">
                              <p className="text-gray-500">No posts found in this category.</p>
                            </div>
                          );
                        }
                      }

                      return categoryData[themeProperties.title]?.length > 0 ? (
                        chunkArray(categoryData[themeProperties.title], 4).map(
                          (chunk: any, chunkIndex) => (
                            <div key={chunkIndex} className="mt-0">
                              {themeProperties.title === "My Feed" ? (
                                <div>
                                  {/* {Object.entries(themes).map(
                                  ([_, innerThemeProperties]) => ( */}
                                  <div>
                                    {/* {chunk[chunkIndex]?.category ===
                                        innerThemeProperties.title && ( */}
                                    <div className=" cursor-pointer">
                                      {/* <p>{chunk[0]?.category}</p> */}
                                      <div className="">
                                        {publicationsData?.posts?.items?.[chunkIndex]
                                          ?.__typename === "Post" && (
                                          <ImageCard
                                            chunk={chunk}
                                            borderColor={themeProperties.backgroundColor}
                                            chunkIndex={chunkIndex}
                                            lensItem1={
                                              publicationsData?.posts.items[
                                                chunkIndex
                                                //@ts-ignore
                                              ]?.metadata
                                            }
                                            postId={publicationsData?.posts.items[chunkIndex]}
                                            lensId={
                                              publicationsData?.posts.items[chunkIndex].author
                                                .address
                                            }
                                            lenscategory={
                                              profileData?.accountsBulk?.[0]?.username?.localName
                                            }
                                            category={themeProperties.backgroundColor}
                                          />
                                        )}
                                      </div>
                                    </div>
                                    {/* )} */}
                                  </div>
                                  {/* )
                                )} */}
                                </div>
                              ) : (
                                <div className=" cursor-pointer">
                                  <div className="">
                                    {publicationsData?.posts?.items?.[chunkIndex]?.__typename ===
                                      "Post" && (
                                      <ImageCard
                                        chunk={chunk}
                                        borderColor={themeProperties.backgroundColor}
                                        chunkIndex={chunkIndex}
                                        lensItem1={
                                          publicationsData?.posts.items[
                                            chunkIndex
                                            //@ts-ignore
                                          ]?.metadata
                                        }
                                        postId={publicationsData?.posts.items[chunkIndex]}
                                        lensId={
                                          publicationsData?.posts.items[chunkIndex].author.address
                                        }
                                        // lensId={profileData?.profiles?.items[0].id}
                                        lenscategory={
                                          profileData?.accountsBulk?.[0]?.username?.localName
                                        }
                                        category={themeProperties.title}
                                      />
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>
                          )
                        )
                      ) : (
                        <div className="flex flex-col justify-center items-center h-[30vh] w-full">
                          <div className="text-gray-400 mb-2">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="48"
                              height="48"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                              <circle cx="8.5" cy="8.5" r="1.5"></circle>
                              <polyline points="21 15 16 10 5 21"></polyline>
                            </svg>
                          </div>
                          <p className="text-xl text-gray-600 font-medium">No Posts Yet</p>
                          <p className="text-gray-400 mt-1">No posts available in this category</p>
                        </div>
                      );
                    })()}
                  </div>
                )}
              </div>
            ))}
          </div>
        </>
      )}
    </>
  );
};

export default PostCompTest;
