import React from "react";
import BrowseUser from ".";
interface BrowseDataProps {
  postId: string;
  userData: any;
  postData: any;
  categoryName: string;
}

const BrowseData: React.FC<BrowseDataProps> = React.memo(
  ({ postId, userData, postData, categoryName }) => {
    console.log({ postData });
    console.log({ userData });
    console.log({ categoryName });
    console.log({ postId });

    return (
      <div>
        <BrowseUser
          isLens={false}
          userId={userData?.user_id}
          postId={postId}
          userData={userData}
          postData={postData}
          categoryName={decodeURIComponent(categoryName)}
        />
      </div>
    );
  }
);

export default BrowseData;
